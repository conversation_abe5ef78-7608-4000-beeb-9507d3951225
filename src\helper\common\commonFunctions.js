import { toast } from 'react-toastify';
import { identifiers } from '@/helper/constants/identifier';
import { fetchFromStorage } from '@/helper/context/storage';
import { Config } from '@/helper/context';
import { loadStripe } from '@stripe/stripe-js';
import moment from 'moment';

// APIs MESSAGE
export const setApiMessage = (type, message) => {
  var commonProps = {
    position: 'bottom-center',
    autoClose: 4000,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
    hideProgressBar: true,
    theme: 'colored',
    style: {
      minHeight: 'unset',
    },
    toastId: 'active',
  };
  switch (type) {
    case 'info':
      toast.info(message, commonProps);
      break;
    case 'success':
      toast.success(message, commonProps);
      break;
    case 'warning':
      toast.warning(message, commonProps);
      break;
    case 'error':
      toast.error(message, commonProps);
      break;
    default:
      break;
  }
};
export const getFields = (list, field) => {
  return list.reduce(function (carry, item) {
    if (typeof item === 'object' && field in item) {
      carry.push(item[field]);
    }
    return carry;
  }, []);
};
export const findDifferences = (obj1, obj2) => {
  let differences = {};

  function compareObjects(o1, o2, parentKey) {
    const keys = new Set([...Object.keys(o1), ...Object.keys(o2)]);

    for (let key of keys) {
      let fullKey = parentKey ? `${parentKey}.${key}` : key;

      if (
        typeof o1[key] === 'object' &&
        o1[key] !== null &&
        typeof o2[key] === 'object' &&
        o2[key] !== null
      ) {
        compareObjects(o1[key], o2[key], fullKey);
      } else if (o1[key] !== o2[key]) {
        differences[fullKey] = { oldValue: o1[key], newValue: o2[key] };
      }
    }
  }

  compareObjects(obj1, obj2, '');
  return differences;
};
export const GraphColors = [
  '#FF0000', // Red
  '#00FF00', // Green
  '#0000FF', // Blue
  // '#FFFF00', // Yellow
  '#FF00FF', // Magenta
  '#00FFFF', // Cyan
  '#FFA500', // Orange
  '#800080', // Purple
  '#FF6347', // Tomato
  '#4682B4', // SteelBlue
  '#32CD32', // LimeGreen
  '#FFD700', // Gold
  '#DC143C', // Crimson
  '#00CED1', // DarkTurquoise
  '#FF1493', // DeepPink
  '#7B68EE', // MediumSlateBlue
  '#ADFF2F', // GreenYellow
  '#FF4500', // OrangeRed
  '#1E90FF', // DodgerBlue
  '#8A2BE2', // BlueViolet
  '#8A2BE2', // BlueViolet
];
export const GeneratePassword = (clength) => {
  const length = clength;
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  const specialCharacters = '@$!%*?&';
  const allCharacters = lowercase + uppercase + numbers + specialCharacters;

  let password = '';
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  password +=
    specialCharacters[Math.floor(Math.random() * specialCharacters.length)];

  for (let i = password.length; i < length; i++) {
    password += allCharacters[Math.floor(Math.random() * allCharacters.length)];
  }
  return password
    .split('')
    .sort(() => 0.5 - Math.random())
    .join('');
};
export const GeneratePin = () => {
  const numbers = '0123456789';
  let pin = '';

  for (let i = 0; i < 4; i++) {
    pin += numbers[Math.floor(Math.random() * numbers.length)];
  }

  return pin;
};
const stringToBrightColor = (string) => {
  let hash = 0;
  let i;

  for (i = 0; i < string?.length; i += 1) {
    hash = string.charCodeAt(i) + ((hash << 5) - hash);
  }

  let color = '#';

  for (i = 0; i < 3; i += 1) {
    let value = (hash >> (i * 8)) & 0xff;
    value = Math.min(value + 128, 255);
    color += `00${value.toString(16)}`.slice(-2);
  }

  return color;
};
const invertColor = (hex) => {
  hex = hex.slice(1);
  const r = (255 - parseInt(hex.slice(0, 2), 16)).toString(16).padStart(2, '0');
  const g = (255 - parseInt(hex.slice(2, 4), 16)).toString(16).padStart(2, '0');
  const b = (255 - parseInt(hex.slice(4, 6), 16)).toString(16).padStart(2, '0');
  return `#${r}${g}${b}`;
};
export const stringAvatar = (name) => {
  const brightColor = stringToBrightColor(name);
  const darkColor = invertColor(brightColor);
  return {
    sx: {
      color: '#FFFFFF',
      bgcolor: darkColor,
    },
    children:
      name?.split(' ')?.[0]?.[0] && name?.split(' ')[1]?.[0]
        ? `${name?.split(' ')?.[0]?.[0].toUpperCase()}${name
            ?.split(' ')[1]?.[0]
            .toUpperCase()}`
        : `${name?.split(' ')?.[0]?.[0].toUpperCase()}`,
  };
};
export const TotalOfDSRObj = (DsrData, index) => {
  let totalAmount = 0;
  let data = DsrData[index];
  data?.payment_type_category?.forEach((category) => {
    if (category?.payment_type_category_pattern === 'multiple') {
      // Sum all dsr_amounts from categoryBranchValue
      category?.categoryBranchValue.forEach((branch) => {
        totalAmount += parseFloat(branch?.dsr_amount)
          ? parseFloat(branch?.dsr_amount)
          : 0;
      });
    } else if (category?.payment_type_category_pattern === 'single') {
      // Directly add the dsr_amount for single pattern
      totalAmount += parseFloat(category?.dsr_amount)
        ? parseFloat(category?.dsr_amount)
        : 0;
    }
  });

  return parseFloat(totalAmount.toFixed(2));
};
export const TotalOfDSRArray = (dsrData) => {
  let totalAmount = 0;

  dsrData &&
    dsrData?.length > 0 &&
    dsrData?.forEach((data) => {
      data?.payment_type_category?.forEach((category) => {
        if (
          category?.payment_type_category_pattern === 'multiple' &&
          data?.has_include_amount
        ) {
          // Sum all dsr_amounts from categoryBranchValue
          category?.categoryBranchValue.forEach((branch) => {
            totalAmount += parseFloat(branch?.dsr_amount)
              ? parseFloat(branch?.dsr_amount)
              : 0;
          });
        } else if (
          category.payment_type_category_pattern === 'single' &&
          data?.has_include_amount
        ) {
          // Directly add the dsr_amount for single pattern
          totalAmount += parseFloat(category?.dsr_amount)
            ? parseFloat(category?.dsr_amount)
            : 0;
        }
      });
    });

  return parseFloat(totalAmount.toFixed(2));
};
export const TotalOfWSRObj = (DsrData, index) => {
  let totalAmount = 0;
  let data = DsrData[index];
  data?.payment_type_category?.forEach((category) => {
    if (category?.payment_type_category_pattern === 'multiple') {
      // Sum all dsr_amounts from categoryBranchValue
      category?.categoryBranchValue.forEach((branch) => {
        totalAmount += parseFloat(branch?.wsr_amount)
          ? parseFloat(branch?.wsr_amount)
          : 0;
      });
    } else if (category?.payment_type_category_pattern === 'single') {
      // Directly add the wsr_amount for single pattern
      totalAmount += parseFloat(category?.wsr_amount)
        ? parseFloat(category?.wsr_amount)
        : 0;
    }
  });

  return parseFloat(totalAmount.toFixed(2));
};
export const TotalOfWSRArray = (dsrData) => {
  let totalAmount = 0; // Single variable to store the total sum

  dsrData &&
    dsrData?.length > 0 &&
    dsrData?.forEach((data) => {
      data?.payment_type_category?.forEach((category) => {
        if (
          category?.payment_type_category_pattern === 'multiple' &&
          data?.has_include_amount
        ) {
          // Sum all wsr_amounts from categoryBranchValue
          category?.categoryBranchValue.forEach((branch) => {
            totalAmount += parseFloat(branch?.wsr_amount)
              ? parseFloat(branch?.wsr_amount)
              : 0;
          });
        } else if (
          category.payment_type_category_pattern === 'single' &&
          data?.has_include_amount
        ) {
          // Directly add the wsr_amount for single pattern
          totalAmount += parseFloat(category?.wsr_amount)
            ? parseFloat(category?.wsr_amount)
            : 0;
        }
      });
    });

  return parseFloat(totalAmount.toFixed(2));
};
export const TotalOfExpenseObj = (DsrData, index) => {
  let totalAmount = 0;
  let data = DsrData[index];
  data?.payment_type_category?.forEach((category) => {
    if (category?.payment_type_category_pattern === 'multiple') {
      // Sum all expense_amount from categoryBranchValue
      category?.categoryBranchValue.forEach((branch) => {
        totalAmount += parseFloat(branch?.expense_amount)
          ? parseFloat(branch?.expense_amount)
          : 0;
      });
    } else if (category?.payment_type_category_pattern === 'single') {
      // Directly add the expense_amount for single pattern
      totalAmount += parseFloat(category?.expense_amount)
        ? parseFloat(category?.expense_amount)
        : 0;
    }
  });

  return parseFloat(totalAmount.toFixed(2));
};
export const TotalOfExpenseArray = (dsrData) => {
  let totalAmount = 0;

  dsrData &&
    dsrData?.length > 0 &&
    dsrData?.forEach((data) => {
      data?.payment_type_category?.forEach((category) => {
        if (
          category?.payment_type_category_pattern === 'multiple' &&
          data?.has_include_amount
        ) {
          // Sum all expense_amounts from categoryBranchValue
          category?.categoryBranchValue.forEach((branch) => {
            totalAmount += parseFloat(branch?.expense_amount)
              ? parseFloat(branch?.expense_amount)
              : 0;
          });
        } else if (
          category.payment_type_category_pattern === 'single' &&
          data?.has_include_amount
        ) {
          // Directly add the expense_amount for single pattern
          totalAmount += parseFloat(category?.expense_amount)
            ? parseFloat(category?.expense_amount)
            : 0;
        }
      });
    });

  return parseFloat(totalAmount.toFixed(2));
};
export const removeVAT = (totalAmount, vatPercentage) => {
  const vatDecimal = vatPercentage / 100;

  const netAmount = totalAmount / (1 + vatDecimal);

  const vatAmount = totalAmount - netAmount;
  return {
    netAmount: parseFloat(netAmount.toFixed(2)),
    vatAmount: parseFloat(vatAmount.toFixed(2)),
  };
};
export const getTextColor = (color) => {
  const hexToRgb = (hex) => {
    const bigint = parseInt(hex.slice(1), 16);
    return {
      r: (bigint >> 16) & 255,
      g: (bigint >> 8) & 255,
      b: bigint & 255,
    };
  };

  const rgbToHex = ({ r, g, b }) =>
    `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;

  const lightenColor = (rgb, percentage) => {
    const amount = (percentage / 100) * 255;
    return {
      r: Math.min(255, Math.round(rgb.r + amount)),
      g: Math.min(255, Math.round(rgb.g + amount)),
      b: Math.min(255, Math.round(rgb.b + amount)),
    };
  };

  const rgb = hexToRgb(color);

  const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;

  let textColor = color;
  let backgroundColor;

  if (brightness < 128) {
    const lighterRgb = lightenColor(rgb, 60);
    textColor = color;
    backgroundColor = rgbToHex(lighterRgb);
  } else {
    const lighterRgb = lightenColor(rgb, 40);
    textColor = color;
    backgroundColor = rgbToHex(lighterRgb);
  }

  if (textColor === '#ffffff') {
    backgroundColor = '#3577a3';
  }

  return {
    textColor,
    backgroundColor,
  };
};

export const rtwlist = [
  {
    name: 'Share code',
    key: 'share_code',
    isRequire: true,
    data: '',
    id: 1,
  },
  {
    name: 'Passport Front',
    key: 'passport_front',
    isRequire: true,
    data: '',
    id: 2,
  },
  {
    name: 'Passport Back',
    key: 'passport_back',
    isRequire: true,
    data: '',
    id: 3,
  },
  {
    name: 'CV',
    key: 'cv',
    isRequire: false,
    data: '',
    id: 4,
  },
  {
    name: 'BRP/E-Visa (JPG, PNG, PDF)',
    key: 'brp_front',
    isRequire: true,
    data: '',
    id: 5,
  },
  {
    name: 'BRP Back',
    key: 'brp_back',
    isRequire: true,
    data: '',
    id: 6,
  },
  {
    name: 'P45',
    Subname: 'If Applicable',
    key: 'p45',
    isRequire: false,
    data: '',
    id: 7,
  },
  {
    name: 'NI Letter',
    key: 'ni_letter',
    isRequire: true,
    data: '',
    id: 8,
  },
  {
    name: 'Students Letter',
    Subname: 'If Applicable',
    key: 'student_letter',
    isRequire: false,
    data: '',
    id: 9,
  },
  {
    name: 'Bank Statement/Utility Bill/Council Bill', //Bank Statements/DL/Utility Bill
    key: 'statements_dl_utility',
    isRequire: true,
    data: '',
    id: 10,
  },
];
export const rtwlistUK = [
  {
    name: 'Photo ID',
    Subname: 'Passport/Birth Certificate/Naturalization Certificate',
    key: 'photoID',
    isRequire: true,
    data: '',
    id: 1,
  },
  {
    name: 'NI Letter',
    Subname: 'Reference letter OR Previous pay slip',
    key: 'ni_letter',
    isRequire: true,
    data: '',
    id: 2,
  },
  {
    name: 'Current Address proof',
    Subname:
      'Utility Bill/Council Tax bill/Bank Statement, from the last 3 months',
    key: 'statements_dl_utility',
    isRequire: true,
    data: '',
    id: 3,
  },
  {
    name: 'P45 ',
    Subname: 'If Applicable',
    key: 'p45',
    isRequire: false,
    data: '',
    id: 4,
  },
  {
    name: 'CV',
    key: 'cv',
    isRequire: false,
    data: '',
    id: 5,
  },
];
export const WorkPermitList = [
  {
    label: 'British National (Overseas) visa',
    value: 'British National (Overseas) Visa',
  },
  {
    label: 'Charity Worker visa (Temporary Work)',
    value: 'Charity Worker Visa (Temporary Work)',
  },
  {
    label: 'Creative Worker visa (Temporary Work)',
    value: 'Creative Worker Visa (Temporary Work)',
  },
  { label: 'Dependent Visa', value: 'Dependent Visa' },
  { label: 'Entrepreneur visa (Tier 1)', value: 'Entrepreneur Visa (Tier 1)' },
  { label: 'Exempt vignette', value: 'Exempt Vignette' },
  { label: 'Frontier Worker permit', value: 'Frontier Worker Permit' },
  { label: 'Global Talent visa', value: 'Global Talent Visa' },
  {
    label: 'Government Authorised Exchange visa (Temporary Work)',
    value: 'Government Authorised Exchange Visa (Temporary Work)',
  },
  {
    label: 'Graduate Trainee visa (Global Business Mobility)',
    value: 'Graduate Trainee Visa (Global Business Mobility)',
  },
  { label: 'Graduate visa', value: 'Graduate Visa' },
  {
    label: 'Health and Care Worker visa',
    value: 'Health and Care Worker Visa',
  },
  {
    label: 'High Potential Individual (HPI) visa',
    value: 'High Potential Individual (HPI) Visa',
  },
  {
    label: 'India Young Professionals Scheme visa',
    value: 'India Young Professionals Scheme Visa',
  },
  { label: 'Innovator Founder visa', value: 'Innovator Founder Visa' },
  {
    label: 'International Agreement visa (Temporary Work)',
    value: 'International Agreement Visa (Temporary Work)',
  },
  {
    label: 'International Sportsperson visa',
    value: 'International Sportsperson Visa',
  },
  { label: 'Investor visa (Tier 1)', value: 'Investor Visa (Tier 1)' },
  {
    label: 'Minister of Religion visa (T2)',
    value: 'Minister of Religion Visa (T2)',
  },
  { label: 'Other', value: 'Other' },
  { label: 'Over seas visa', value: 'Overseas Visa' },
  {
    label: 'Overseas Domestic Worker visa',
    value: 'Overseas Domestic Worker Visa',
  },
  {
    label: 'Religious Worker visa (Temporary Work)',
    value: 'Religious Worker Visa (Temporary Work)',
  },
  {
    label: 'Representative of an Overseas Business visa',
    value: 'Representative of an Overseas Business Visa',
  },
  { label: 'Scale-up Worker visa', value: 'Scale-up Worker Visa' },
  { label: 'Scale worker visa', value: 'Scale worker visa' },
  {
    label: 'Seasonal Worker visa (Temporary Work)',
    value: 'Seasonal Worker Visa (Temporary Work)',
  },
  {
    label: 'Secondment Worker visa (Global Business Mobility)',
    value: 'Secondment Worker Visa (Global Business Mobility)',
  },
  {
    label: 'Senior or Specialist Worker visa (Global Business Mobility)',
    value: 'Senior or Specialist Worker Visa (Global Business Mobility)',
  },
  {
    label: 'Service providers from Switzerland visa',
    value: 'Service Providers from Switzerland Visa',
  },
  {
    label: 'Service Supplier visa (Global Business Mobility)',
    value: 'Service Supplier Visa (Global Business Mobility)',
  },
  { label: 'Skilled Worker visa', value: 'Skilled Worker Visa' },
  { label: 'Start-up visa', value: 'Start-up Visa' },
  { label: 'Student', value: 'Student' },
  { label: 'Tire-1', value: 'Tier-1' },
  { label: 'Tire-2', value: 'Tier-2' },
  {
    label: 'Turkish Businessperson visa',
    value: 'Turkish Businessperson Visa',
  },
  { label: 'Turkish Worker visa', value: 'Turkish Worker Visa' },
  { label: 'UK Ancestry visa', value: 'UK Ancestry Visa' },
  {
    label: 'UK Expansion Worker visa (Global Business Mobility)',
    value: 'UK Expansion Worker Visa (Global Business Mobility)',
  },
  { label: 'Youth Mobility Scheme visa', value: 'Youth Mobility Scheme Visa' },
];
export const contentType = [
  { label: 'Document', value: 'document' },
  { label: 'Training', value: 'training' },
];
export const DsrCategoryType = [
  { label: 'Income', value: 'income' },
  { label: 'Other', value: 'other' },
  { label: 'Expense', value: 'expense' },
];
export const DsrSubCategoryType = [
  { label: 'Single', value: 'single' },
  { label: 'Multiple', value: 'multiple' },
];
export const DSRValueType = [
  { label: 'Number', value: 'number' },
  { label: 'String', value: 'string' },
];
export const DSRFieldStatus = [
  { label: 'Active', value: 'active' },
  { label: 'In-Active', value: 'inactive' },
  { label: 'Delete', value: 'delete' },
];

export const generateYearsFromJoiningDate = (userJoiningDate) => {
  if (!userJoiningDate) return [];

  const joiningYear = new Date(userJoiningDate).getFullYear();
  const currentYear = new Date().getFullYear();

  if (joiningYear > currentYear) return []; // Handle future dates

  return Array.from({ length: currentYear - joiningYear + 1 }, (_, i) => {
    const year = joiningYear + i;
    return { label: year, value: year };
  });
};
export const getAuthBlobUrl = async (url) => {
  if (!url) return null;
  const token = fetchFromStorage(identifiers.AUTH_DATA)?.token;
  if (!token) throw new Error('Authorization token not found');

  const headers = { Authorization: `Bearer ${token}` };

  try {
    const response = await fetch(url, { headers });

    if (!response.ok) {
      throw new Error(`Failed to fetch resource: ${response.statusText}`);
    }

    const blob = await response.blob();
    return URL.createObjectURL(blob);
  } catch (error) {
    console.error('Error fetching resource with auth:', error);
    throw error;
  }
};
export const GenerateTokenLink = (userJoiningDate) => {
  if (!userJoiningDate) return [];

  const joiningYear = new Date(userJoiningDate).getFullYear();
  const currentYear = new Date().getFullYear();

  if (joiningYear > currentYear) return []; // Handle future dates

  return Array.from({ length: currentYear - joiningYear + 1 }, (_, i) => {
    const year = joiningYear + i;
    return { label: year, value: year };
  });
};
export const getEnvPrefix = () => {
  const prefixes = {
    staging: 'Staging - ',
    testing: 'Testing - ',
  };

  const env = process.env.NEXT_PUBLIC_ENVIRONMENT?.toLowerCase();
  return prefixes[env ?? ''] || '';
};
export const generateMetadata = ({ pageTitle }) => {
  const envPrefix = getEnvPrefix();
  const seoTitle = identifiers?.SEO_TITLE || identifiers?.APP_NAME;
  // const fullTitle = `${pageTitle} | ${seoTitle}`;
  const fullTitle = `${envPrefix}${pageTitle} | ${seoTitle}`;
  const description = `${pageTitle} page of ${seoTitle}`;
  const imageUrl = `${Config?.baseURL}/images/favicon.svg`;

  return {
    title: fullTitle,
    description: description,
    ogImageUrl: imageUrl,
    openGraph: {
      title: fullTitle,
      description: description,
      images: [{ url: imageUrl }],
    },
    twitter: {
      title: fullTitle,
      description: description,
      images: [imageUrl],
    },
  };
};
export const formatDurationWithType = (days, durationType) => {
  if ((durationType === 'Weekly' || durationType === 'weekly') && days < 7) {
    return `${days} day${days !== 1 ? 's' : ''}`;
  } else if (
    (durationType === 'Weekly' || durationType === 'weekly') &&
    days < 30
  ) {
    const weeks = Math.round(days / 7);
    return `${weeks} week${weeks !== 1 ? 's' : ''}`;
  } else if (
    (durationType === 'Monthly' || durationType === 'monthly') &&
    days < 365
  ) {
    const months = Math.round(days / 30.44); // average days per month
    return `${months} month${months !== 1 ? 's' : ''}`;
  } else {
    const years = Math.round(days / 365.25);
    return `${years} year${years !== 1 ? 's' : ''}`;
  }
};
export const formatDuration = (days) => {
  if (days < 7) {
    return `${days} day${days !== 1 ? 's' : ''}`;
  } else if (days < 30) {
    const weeks = Math.round(days / 7);
    return `${weeks} week${weeks !== 1 ? 's' : ''}`;
  } else if (days < 365) {
    const months = Math.round(days / 30.44); // average days per month
    return `${months} month${months !== 1 ? 's' : ''}`;
  } else {
    const years = Math.round(days / 365.25);
    return `${years} year${years !== 1 ? 's' : ''}`;
  }
};
export const checkOrganizationRole = (isOrg = 'org_master') => {
  const isOrganisation =
    typeof window !== 'undefined' &&
    localStorage.getItem(identifiers?.AUTH_DATA) &&
    JSON.parse(localStorage.getItem(identifiers?.AUTH_DATA)) &&
    JSON.parse(localStorage.getItem(identifiers?.AUTH_DATA))?.user_roles?.find(
      (r) => r?.name === isOrg
    );
  return !!isOrganisation;
};
export const stripePromise = loadStripe(Config?.PublicKeyStripe);
export const DateFormat = (dates, type) => {
  if (type === 'datesWithhourUTC') {
    return moment.utc(dates).local().format('DD-MM-YYYY hh:mm A');
  } else if (type === 'datesWithhour') {
    return moment(dates).format('DD-MM-YYYY hh:mm A');
  } else if (type === 'datesUTC') {
    return moment.utc(dates).local().format('DD-MM-YYYY');
  } else if (type === 'hoursUTC') {
    return moment.utc(dates).local().format('hh:mm A');
  } else if (type === 'dates') {
    return moment(dates).format('DD-MM-YYYY');
  }
  return moment(dates).format('DD-MM-YYYY');
};
export const dayOptions = [
  ...Array.from({ length: 31 }, (_, i) => {
    const day = i + 1;
    const suffix = ['th', 'st', 'nd', 'rd'][
      day % 10 > 3 || ~~((day % 100) / 10) === 1 ? 0 : day % 10
    ];
    return { label: `${day}${suffix}`, value: day };
  }),
  // { label: `Last Day - 31st`, value: '31' },
  { label: 'Joining Date', value: 'joining_date' },
];
export const monthsOption = Array.from({ length: 12 }, (_, i) => {
  const date = new Date(0, i);
  return {
    label: date.toLocaleString('default', { month: 'long' }),
    value: i + 1,
  };
});
export const monthsDays = Array.from({ length: 12 }, (_, i) =>
  new Date(0, i).toLocaleString('default', { month: 'long' })
);

const BRANCH_MANAGER_ROLE_IDS = [7, 14];
export const HIGH_LEVEL_MANAGER_ROLE_IDS = [1, 2, 3, 4, 5, 6];

const HIGH_LEVEL_MANAGER_ROLE_ID = [1, 2, 3, 4, 6];

export const TextFieldMaxLength = 50;
/**
 * Returns whether the current user is a branch manager
 * and, if so, what their branch ID is.
 */
export const getBranchManagerInfo = (authState) => {
  const roleId = authState?.web_user_active_role_id;
  const isBranchManager = BRANCH_MANAGER_ROLE_IDS.includes(roleId);
  return {
    isBranchManager,
    branchId: isBranchManager ? (authState?.branch?.id ?? null) : null,
  };
};

export const getHighLevelManager = (userData) => {
  const roleId = userData?.web_user_active_role_id;
  const isHighLevelManager = HIGH_LEVEL_MANAGER_ROLE_ID.includes(roleId);
  return {
    isHighLevelManager,
  };
};

export const getImageName = (fullPath) => {
  const parts = fullPath.split('/');
  return parts[parts.length - 1];
};

export function getCurrencySymbol(currencyDetails) {
  return currencyDetails?.symbol || '£';
}

// allowed only numbers
export const allowedOnlyNumbers = (e) => {
  return (e.target.value = e.target.value.replace(/[^0-9.]/g, ''));
};

// Ingredient Icon Size
export const IngredientIconSize = 24;

// Utility function to determine if current route is public or private
// Public route: pathname matches /recipe/[org_name] (e.g., /recipe/org1, /recipe/org1/recipe-preview/123)
// Private route: pathname contains '/recipes' (e.g., /recipes, /recipes/list)
export const isPublicRoute = (pathname) => {
  return /^\/recipe\/[^/]+/.test(pathname) && !pathname.includes('/recipes');
};

// Utility function to conditionally fetch data based on route type
// For public routes, it calls the public API function or returns fallback value
// For private routes, it calls the private API function
export const conditionalApiCall = async (
  pathname,
  privateApiFunction,
  publicApiFunction = null,
  fallbackValue = []
) => {
  const isPublic = isPublicRoute(pathname);

  if (!isPublic) {
    // Call the private API function for private routes
    try {
      return await privateApiFunction();
    } catch (error) {
      console.error('Private API call failed:', error);
      return fallbackValue;
    }
  } else {
    if (publicApiFunction && typeof publicApiFunction === 'function') {
      // Call public API function for public routes
      try {
        return await publicApiFunction();
      } catch (error) {
        console.error('Public API call failed:', error);
        return fallbackValue;
      }
    } else {
      // Return fallback value if no public API function provided
      return fallbackValue;
    }
  }
};

// Utility function to get CSS class based on route type
// Helps maintain consistent styling between public and private pages
export const getRouteBasedClassName = (
  pathname,
  baseClassName,
  publicSuffix = '--public'
) => {
  const isPublic = isPublicRoute(pathname);
  return isPublic ? `${baseClassName}${publicSuffix}` : baseClassName;
};

// Utility function to check if certain features should be available based on route type
// Returns an object with feature availability flags
export const getRouteFeatures = (pathname) => {
  const isPublic = isPublicRoute(pathname);

  return {
    isPublicRoute: isPublic,
    showRecipeMenu: !isPublic,
    showBookmarkFunctionality: !isPublic,
    showDraftPublishedStatus: !isPublic,
    showEditIcons: !isPublic,
    allowEdit: !isPublic,
    allowDelete: !isPublic,
    allowDuplicate: !isPublic,
    showPrivateFilters: !isPublic,
  };
};

// This function helps maintain consistency across components for public/private API logic
export const getRecipeListByRouteType = async (
  pathname,
  search,
  page,
  Rpp,
  filter,
  getPublicRecipeList,
  getRecipeList
) => {
  const isPublic = isPublicRoute(pathname);

  if (!isPublic) {
    // Call private API for private routes (e.g., /recipes) - default behavior
    return await getRecipeList(search || '', page || 1, Rpp || 10, filter, {
      key: 'updated_at',
      value: 'DESC',
    });
  } else {
    // Call public API for public routes (e.g., /recipe)
    return await getPublicRecipeList(
      search || '',
      page || 1,
      Rpp || 10,
      filter
    );
  }
};

export const getFormattedTotalTime = (cookTotalTime) => {
  const totalMinutes = Math.floor(cookTotalTime);
  const days = Math.floor(totalMinutes / (24 * 60));
  const hours = Math.floor((totalMinutes % (24 * 60)) / 60);
  const minutes = totalMinutes % 60;

  let result = '';
  if (days > 0) result += `${days}d `;
  if (hours > 0) result += `${hours}h `;
  if (minutes > 0 || result === '') result += `${minutes}m`;

  return result.trim();
};

export const statusClassName = (status) => {
  const map = {
    rejected: 'failed',
    deleted: 'failed',
    cancelled: 'cancelled',
    ongoing: 'ongoing',
    draft: 'draft',
    pending: 'draft',
    completed: 'ongoing',
    verified: 'success',
  };
  return map[status] || 'success';
};
