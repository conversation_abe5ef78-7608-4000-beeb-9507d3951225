'use client';
// import CustomSelect from '@/components/UI/selectbox';
import { useContext, useEffect, useState } from 'react';
import KeyboardArrowLeftIcon from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import AvailabilityDashboardCalendar from './AvailabilityDashboardCalendar';
import moment from 'moment';
import { DesktopDatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { setApiMessage } from '@/helper/common/commonFunctions';
import PreLoader from '@/components/UI/Loader';
import { ROTA_URLS } from '@/helper/constants/urls';
import AuthContext from '@/helper/authcontext';
import TodayIcon from '@mui/icons-material/Today';
import { Tooltip, Typography } from '@mui/material';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import CustomButton from '@/components/UI/CustomButton';
import CustomSelect from '@/components/UI/CustomSelect';
import ContentLoader from '@/components/UI/ContentLoader';

const AvailabilityDashboardFilter = () => {
  const { authState } = useContext(AuthContext);
  const [filterData, setFilterData] = useState({
    currentDate: moment(new Date()).format('YYYY-MM-DD'),
    selectedUser: '',
  });
  const [anchorEl, setAnchorEl] = useState(null);
  const [openDatePicker, setOpenDatePicker] = useState(false);
  const [isLoader, setIsLoader] = useState(false);
  const [loader, setloader] = useState(false);
  const [availabilityList, setAvailabilityList] = useState([]);
  const [staffListData, setStaffListData] = useState([]);

  const viewAccessOnly = authState?.web_user_active_role_id
    ? authState.web_user_active_role_id === 1 ||
      authState.web_user_active_role_id === 2 ||
      authState.web_user_active_role_id === 7
    : '';

  // User list
  const getStaffList = async () => {
    setIsLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        ROTA_URLS?.SHIFT_STAFF_LIST + `?isAdmin=false&isRotaList=true`
      );

      if (status === 200) {
        setIsLoader(false);
        const filterOption = [];
        filterOption.push(
          ...data?.userList?.map((user) => ({
            label: user?.user_full_name,
            value: user?.id,
          }))
        );
        setStaffListData(filterOption);
        setFilterData((prev) => ({
          ...prev,
          selectedUser: filterOption[0]?.value,
        }));
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setIsLoader(false);
      setStaffListData([]);
    }
  };

  // List of dashboard availability
  const getDashboardAvailabilityList = async (isUpdated) => {
    let filterList = '';

    const firstDay = moment(filterData?.currentDate)
      .startOf('month')
      .format('YYYY-MM-DD');
    const lastDay = moment(filterData?.currentDate)
      .endOf('month')
      .format('YYYY-MM-DD');

    filterList = `?startDate=${firstDay}&endDate=${lastDay}`;

    filterList += `&isAdmin=true&userId=${filterData?.selectedUser === '' ? '' : filterData?.selectedUser}`;

    try {
      isUpdated && setIsLoader(true);
      setloader(true);
      const { status, data } = await axiosInstance.get(
        ROTA_URLS?.AVAILABILITY_URL + filterList
      );
      if (status === 200) {
        setIsLoader(false);
        setloader(false);
        const monthShiftEventsUpdate = data?.data?.map((event) => {
          return {
            ...event,
            start: event?.date,
            allDay: true,
          };
        });

        setAvailabilityList(monthShiftEventsUpdate);
      }
    } catch (err) {
      setApiMessage('error', err?.response?.data?.message);
      setIsLoader(false);
      setloader(false);
    }
  };

  useEffect(() => {
    getStaffList();
  }, []);
  useEffect(() => {
    if (filterData?.selectedUser !== '') {
      getDashboardAvailabilityList(true);
    }
  }, [filterData?.currentDate, filterData?.selectedUser]);

  const handlePrev = () => {
    setFilterData((prev) => ({
      ...prev,
      currentDate: moment(prev.currentDate || new Date())
        .subtract(1, 'months')
        .format('YYYY-MM-DD'),
    }));
  };

  const handleNext = () => {
    setFilterData((prev) => ({
      ...prev,
      currentDate: moment(prev.currentDate || new Date())
        .add(1, 'months')
        .format('YYYY-MM-DD'),
    }));
  };

  const handleDateClick = (selectedDate) => {
    setFilterData({
      ...filterData,
      currentDate: moment(selectedDate).format('YYYY-MM-DD'), // Update date
    });
  };

  return (
    <div>
      {loader && <PreLoader />}
      <div className="dashboard-filter-section">
        <div className="filter-wrap">
          <div className="select-box location-select-field">
            <CustomSelect
              placeholder="All Staffs"
              options={
                viewAccessOnly
                  ? staffListData
                  : staffListData?.filter(
                      (item) => item?.value === authState?.id
                    )
              }
              value={staffListData?.find(
                (item) => item?.value === filterData?.selectedUser
              )}
              onChange={(e) => {
                setFilterData({
                  ...filterData,
                  selectedUser: e.value,
                });
              }}
              menuPosition="fixed"
              isClearable={false}
            />
          </div>
          <div className="datepicker-filter">
            <CustomButton
              variant="outlined"
              isIconOnly
              startIcon={
                <Tooltip
                  title={
                    <Typography className="sub-title-text">
                      Go to current month
                    </Typography>
                  }
                  classes={{
                    tooltip: 'info-tooltip-container',
                  }}
                  arrow
                >
                  <TodayIcon />
                </Tooltip>
              }
              onClick={() => handleDateClick(new Date())}
            />
            <div className="calendar-header">
              <KeyboardArrowLeftIcon
                className="cursor-pointer"
                onClick={handlePrev}
              />
              <div
                onClick={(event) => {
                  setAnchorEl(event.currentTarget);
                  setOpenDatePicker(true);
                }}
                className="cursor-pointer current-month-year-text"
              >
                {
                  moment(filterData.currentDate || new Date()).format(
                    'MMMM YYYY'
                  ) // Example: February 2025
                }

                <ExpandMoreIcon size="small" />
              </div>
              <KeyboardArrowRightIcon
                className="cursor-pointer"
                onClick={handleNext}
              />
            </div>
            <LocalizationProvider
              dateAdapter={AdapterDayjs}
              dateFormats={{ monthShort: 'MMMM' }}
            >
              <DesktopDatePicker
                open={openDatePicker}
                onClose={() => setOpenDatePicker(false)}
                // className="calendar-year-month-select-field"
                value={
                  filterData?.currentDate
                    ? dayjs(filterData?.currentDate, 'YYYY-MM-DD')
                    : null
                }
                onChange={(newValue) => {
                  if (newValue) {
                    setFilterData((prev) => ({
                      ...prev,
                      currentDate: dayjs(newValue).format('YYYY-MM-DD'),
                    }));
                  }
                  setOpenDatePicker(false);
                }}
                slotProps={{
                  textField: { style: { display: 'none' } }, // Hide input field
                  popper: {
                    anchorEl,
                    placement: 'bottom',
                    className: 'calendar-year-month-select-field',
                  },
                }}
                views={['month', 'year']}
              />
            </LocalizationProvider>
          </div>
        </div>
      </div>
      <div className="availability-calendar-view">
        <div className="availability-calendar">
          {isLoader ? (
            <ContentLoader />
          ) : (
            <AvailabilityDashboardCalendar
              filterData={filterData}
              availabilityList={availabilityList}
              getDashboardAvailabilityList={getDashboardAvailabilityList}
            />
          )}
        </div>
      </div>
    </div>
  );
};
export default AvailabilityDashboardFilter;
